package enums

import "time"

const (
	FileSizeBYTE = 1
	FileSizeKB   = FileSizeBYTE * 1024
	FileSizeMB   = FileSizeKB * 1024
	FileSizeGB   = FileSizeMB * 1024
)

const (
	CompressedFilePath = "user-compressed"
)

const (
	CompressedFileExpire = time.Second * 60 * 60 * 24
)

const (
	FileOptUriSizeBatch        = "/file/size_batch"
	FileOptUriCompress         = "/file/compress"
	FileOptUriCompressProgress = "/file/compress_progress"
)

type ModelSource uint

const (
	ModelSourceHuggingFace ModelSource = iota + 1
	ModelSourceCivitai
)

const (
	FlashUploadFileExist = iota + 1
	FlashUploadFileNotExist
)

type DirShareType uint

// ModelDownloadStatus 第三方模型下载任务状态枚举
const (
	ModelDownloadStatusCompleted = iota + 1
	ModelDownloadStatusFailed
	ModelDownloadStatusDownloading
	ModelDownloadStatusCanceled
)
