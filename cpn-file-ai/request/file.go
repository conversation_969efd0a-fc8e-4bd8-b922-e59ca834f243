package request

import "cpn-file-ai/enums"

type FilesReq struct {
	Action       string `json:"action"` //GetDir CreateDir
	Path         string `json:"path"`
	NewName      string `json:"new_name"`
	NewPath      string `json:"new_path"` //移动文件或者文件夹时
	Filename     string `json:"filename"`
	Catalogue    string `json:"catalogue"`
	Redirect     string `json:"redirect"`
	PodUuid      string `json:"pod_uuid"`
	InstanceUuid string `json:"instance_uuid"`
}

type CompressFileReq struct {
	FilePaths []string `json:"file_paths"` // 要压缩的文件路径
}

type GetFileCompressProgressReq struct {
	FileUuid string `form:"file_uuid"`
}

type FileCompressDownloadReq struct {
	FileUuid string `form:"file_uuid"`
}

type ThirdModelDownloadReq struct {
	PodUuid          string            `json:"pod_uuid"`
	ModelSource      enums.ModelSource `json:"model_source"`       // 模型来源 1. huggingface 2. civitai
	ModelDownloadUrl string            `json:"model_download_url"` // 模型地址
}

type FlashUploadCheckReq struct {
	FileHashHeader string `json:"file_hash_header"`
	FileHashMiddle string `json:"file_hash_middle"`
	FileHashTail   string `json:"file_hash_tail"`
}

type FlashUploadReq struct {
	PodUuid        string `json:"pod_uuid"`
	InstanceUuid   string `json:"instance_uuid"`
	Catalogue      string `json:"catalogue"`
	Filename       string `json:"filename"`
	Path           string `json:"path"`
	FileHashHeader string `json:"file_hash_header"`
	FileHashMiddle string `json:"file_hash_middle"`
	FileHashTail   string `json:"file_hash_tail"`
}

type GetDownloadTasksReq struct {
	PodUuid string `form:"pod_uuid"`
}

type StopDownloadTaskReq struct {
	TaskUuid string `json:"task_uuid"`
}

type GetDownloadProgress struct {
	TaskUuid string `json:"task_uuid"`
}
