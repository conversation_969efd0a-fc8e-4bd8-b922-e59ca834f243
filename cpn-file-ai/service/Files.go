package service

import (
	"context"
	file_ctx "cpn-file-ai/common/file-ctx"
	"cpn-file-ai/common/jsontime"
	"cpn-file-ai/common/logger"
	"cpn-file-ai/common/redis-client"
	"cpn-file-ai/common/utils"
	"cpn-file-ai/config"
	"cpn-file-ai/enums"
	"cpn-file-ai/model"
	"cpn-file-ai/request"
	"cpn-file-ai/response"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"io"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type file_ struct {
}

var FileService file_

const (
	civitaiAPIBaseURL = "https://civitai.com/api/v1"

	huggingfaceAPIURL = "https://huggingface.co/api/models"
	downloadBaseURL   = "https://huggingface.co"

	userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
)

func (f file_) DownloadThirdModel(req request.ThirdModelDownloadReq, userId uint) error {
	var (
		err            error
		modelFileList  []ModelFileListResponse
		modelShaList   []string
		shareModelFile model.ShareModel
		user           model.User
		modelName      string
		downloadTask   model.ThirdModelDownloadTask
		modelFileSize  float32
	)

	// 获取用户信息
	if err = user.GetById(userId); err != nil {
		logger.Error(fmt.Sprintf("获取用户信息失败,err: %v", err))
		return errors.New("获取用户信息失败")
	}
	if user.ID == 0 {
		return errors.New("用户不存在")
	}

	// 根据来源获取模型文件列表
	if req.ModelSource == enums.ModelSourceHuggingFace {
		modelFileList, modelName, err = f.getHuggingFaceModelFile(req.ModelDownloadUrl)
		if err != nil {
			err = errors.New("获取模型文件列表失败")
			logger.Error(fmt.Sprintf("获取模型文件列表失败,err: %v", err))
			return err
		}
	} else if req.ModelSource == enums.ModelSourceCivitai {
		modelFileList, modelName, err = f.getCivitaiModelFile(req.ModelDownloadUrl)
		if err != nil {
			logger.Error(fmt.Sprintf("获取模型文件列表失败,err: %v", err))
			return errors.New("获取模型文件列表失败，请检查url")
		}
	} else {
		return errors.New("不支持的模型来源")
	}

	for _, modelFile := range modelFileList {
		modelShaList = append(modelShaList, modelFile.Sha256)
		modelFileSize += float32(modelFile.FileSize)
	}

	// 对比数据库中的sha256，获取已存在的文件
	shareModelFileList, err := shareModelFile.ListBySha256In(nil, modelShaList)
	if err != nil {
		// 获取失败则忽略，意味着全部文件都要下载
		logger.Error(fmt.Sprintf("查询数据库失败,err: %v", err))
	}

	shareModelFileMap := make(map[string]string)
	for _, shareFile := range shareModelFileList {
		shareModelFileMap[shareFile.SHA256] = shareFile.OriginalPath
	}

	var pod model.Pod
	if err = pod.GetByUuid(req.PodUuid); err != nil {
		logger.Error(fmt.Sprintf("查询Pod失败,err: %v", err))
		return errors.New("下载失败，未找到Pod应用信息")
	}
	if pod.Status == model.PodDeleted || pod.ID == 0 {
		return errors.New("该Pod应用不存在")
	}

	// 检查同一用户下的同一个pod是否已有正在进行的下载任务
	var existingTask model.ThirdModelDownloadTask
	if err = existingTask.GetRunningTaskByUserAndPod(userId, req.PodUuid); err == nil {
		return errors.New("该Pod下已有正在进行的下载任务，请等待完成后再试")
	}

	var (
		modelPath        string // pod挂载的模型根目录
		podBasePath      string // pod挂载目录在主机的位置
		downloadBasePath string // 模型下载目录
		taskUuid         string // 下载任务uuid
	)

	if strings.Contains(pod.Catalogue, DirKeyComfyPoddataModels) {
		modelPath = "ComfyUI/models"
	} else if strings.Contains(pod.Catalogue, DirKeySdPoddataModels) {
		modelPath = "stable-diffusion-webui/models"
	} else if strings.Contains(pod.Catalogue, DirKeyForgePoddataModels) {
		modelPath = "stable-diffusion-webui-forge/models"
	} else {
		return errors.New("未挂载主模型目录，请先挂载主模型目录")
	}
	podBasePath = filepath.Join("/mnt/pod-data", pod.Uuid, modelPath)

	// 创建模型名称文件夹
	downloadBasePath = filepath.Join(podBasePath, modelName)
	if err := utils.EnsureDirExists(downloadBasePath); err != nil {
		logger.Error(fmt.Sprintf("创建模型文件夹失败,err: %v", err))
		return errors.New("创建模型文件夹失败")
	}

	taskUuid = utils.GetUUID()
	// 创建下载任务记录
	downloadTask = model.ThirdModelDownloadTask{
		TaskUuid:         taskUuid,
		UserId:           userId,
		PodUuid:          req.PodUuid,
		DownloadUrl:      req.ModelDownloadUrl,
		DownloadPath:     downloadBasePath,
		DownloadFileSize: modelFileSize,
		Status:           enums.ModelDownloadStatusDownloading,
		Msg:              "下载中",
	}

	// 保存任务记录
	if err = downloadTask.Save(); err != nil {
		logger.Error(fmt.Sprintf("创建下载任务记录失败,err: %v", err))
		return errors.New("创建下载任务失败")
	}

	logger.Info(fmt.Sprintf("创建下载任务成功, taskUuid: %s", taskUuid))

	go func() {
		var (
			totalFiles       = len(modelFileList)
			finalStatus uint = enums.ModelDownloadStatusCompleted
			finalMsg    string
		)
		ctxKey := taskUuid
		redisKey := enums.RedisKeyEnum.FileDownloadKey + ctxKey
		ctx := context.WithValue(context.Background(), "TaskUuid", ctxKey)
		ctx, cancel := context.WithCancel(ctx)

		file_ctx.FileDownloadCtxInstance.Save(ctxKey, ctx, cancel)

		defer func() {
			status, _ := redis_client.RedisGet(redisKey)
			if status == strconv.Itoa(enums.ModelDownloadStatusCanceled) {
				finalStatus = enums.ModelDownloadStatusCanceled
				finalMsg = "用户取消下载"
			} else {
				// 更新任务状态和下载信息
				if err := downloadTask.UpdateStatus(finalStatus, finalMsg); err != nil {
					logger.Error(fmt.Sprintf("更新任务状态失败,err: %v", err))
				}
			}

			file_ctx.FileDownloadCtxInstance.Done(ctxKey)
			redis_client.RedisDel(redisKey)

			logger.Info(fmt.Sprintf("任务完成 taskUuid: %s, 状态: %d, 消息: %s", taskUuid, finalStatus, finalMsg))
		}()

		for i, modelFile := range modelFileList {
			logger.Info(fmt.Sprintf("处理文件 %d/%d: %s", i+1, totalFiles, modelFile.FileName))
			// 文件实际下载路径
			fileDownPathAbs := filepath.Join(downloadBasePath, modelFile.FileName)

			// 如果SHA256为空，直接下载
			if modelFile.Sha256 == "" {
				err = f.downloadFile(ctx, modelFile.FileDownloadUrl, fileDownPathAbs, req.ModelSource)
				if err != nil {
					logger.Error(fmt.Sprintf("文件 %s 下载失败,err: %v", modelFile.FileName, err))
					finalStatus = enums.ModelDownloadStatusFailed
					finalMsg = fmt.Sprintf("文件 %s 下载失败", modelFile.FileName)
					return
				}
				continue
			}

			if shareFilePath, ok := shareModelFileMap[modelFile.Sha256]; !ok {
				// 如果sha256不在数据库中，则下载
				err = f.downloadFile(ctx, modelFile.FileDownloadUrl, fileDownPathAbs, req.ModelSource)
				if err != nil {
					logger.Error(fmt.Sprintf("文件 %s 下载失败,err: %v", modelFile.FileName, err))
					finalStatus = enums.ModelDownloadStatusFailed
					finalMsg = fmt.Sprintf("文件 %s 下载失败", modelFile.FileName)
					return
				}
			} else {
				if shareFilePath == "" {
					logger.Error(fmt.Sprintf("数据库中sha256对应的文件路径为空,sha256: %s", modelFile.Sha256))
					err = f.downloadFile(ctx, modelFile.FileDownloadUrl, fileDownPathAbs, req.ModelSource)
					if err != nil {
						logger.Error(fmt.Sprintf("文件 %s 下载失败,err: %v", modelFile.FileName, err))
						finalStatus = enums.ModelDownloadStatusFailed
						finalMsg = fmt.Sprintf("文件 %s 下载失败", modelFile.FileName)
						return
					}
				} else {
					if err = utils.CreateSymbolLink(shareFilePath, fileDownPathAbs); err != nil {
						logger.Error(fmt.Sprintf("创建软链接失败,err: %v", err))
						// 创建失败就去下载文件
						err = f.downloadFile(ctx, modelFile.FileDownloadUrl, fileDownPathAbs, req.ModelSource)
						if err != nil {
							logger.Error(fmt.Sprintf("文件 %s 下载失败,err: %v", modelFile.FileName, err))
							finalStatus = enums.ModelDownloadStatusFailed
							finalMsg = fmt.Sprintf("文件 %s 下载失败", modelFile.FileName)
							return
						}
					} else {
						logger.Info(fmt.Sprintf("成功创建软链接: %s -> %s", shareFilePath, fileDownPathAbs))
					}
				}
			}
		}
		file_ctx.FileDownloadCtxInstance.Cancel(ctxKey)
	}()
	return nil
}

func (f file_) StopThirdModelDownloadTask(req request.StopDownloadTaskReq, userId uint) error {
	var (
		modelDownloadTask model.ThirdModelDownloadTask
		user              model.User
		err               error
	)
	if err = user.GetById(userId); err != nil {
		logger.Error(fmt.Sprintf("查询用户信息失败,userId: %d", userId))
		return errors.New("获取用户信息失败")
	}
	if user.ID == 0 {
		return errors.New("用户不存在")
	}

	if err = modelDownloadTask.GetByTaskUuid(req.TaskUuid); err != nil {
		logger.Error(fmt.Sprintf("查询下载任务失败,taskUuid: %s", req.TaskUuid))
		return errors.New("停止下载任务失败")
	}
	if modelDownloadTask.ID == 0 {
		return errors.New("下载任务不存在")
	}
	if modelDownloadTask.UserId != userId {
		return errors.New("暂无权限")
	}

	ctx := context.Background()
	if err = redis_client.RedisPub(ctx, file_ctx.RedisSubChannelStopDownload, req.TaskUuid); err != nil {
		logger.Error(fmt.Sprintf("发布停止下载任务失败,taskUuid: %s", req.TaskUuid))
		return errors.New("停止下载任务失败")
	}

	if err = modelDownloadTask.UpdateStatus(enums.ModelDownloadStatusCanceled, "用户取消下载"); err != nil {
		logger.Error(fmt.Sprintf("更新任务状态失败,err: %v", err))
		return errors.New("停止下载任务失败")
	}

	return nil
}

func (f file_) GetThirdModelDownloadTasks(req request.GetDownloadTasksReq, userId uint) (response.GetDownloadTasksResp, error) {
	var (
		resp  response.GetDownloadTasksResp
		tasks []model.ThirdModelDownloadTask
	)

	// 查询用户在指定Pod下的所有下载任务
	err := model.DB.Where("user_id = ? AND pod_uuid = ?", userId, req.PodUuid).
		Order("id desc").
		Find(&tasks).Error
	if err != nil {
		logger.Error(fmt.Sprintf("查询下载任务失败,err: %v", err))
		return resp, errors.New("查询下载任务失败")
	}

	// 转换为响应格式
	resp.Tasks = make([]response.DownloadTaskItem, len(tasks))
	for i, task := range tasks {
		resp.Tasks[i] = response.DownloadTaskItem{
			ID:               task.ID,
			TaskUuid:         task.TaskUuid,
			DownloadUrl:      task.DownloadUrl,
			DownloadFileSize: fmt.Sprintf("%.2fMB", task.DownloadFileSize/1024),
			Status:           task.Status,
			Msg:              task.Msg,
			StartAt:          task.CreatedAt.Format("2006-01-02 15:04:05"),
		}
	}

	return resp, nil
}

func (f file_) GetThirdModelDownloadProgress(req request.GetDownloadProgress) (response.GetFileCompressProgressResp, error) {
	var (
		downloadTask model.ThirdModelDownloadTask
		resp         response.GetFileCompressProgressResp
		err          error
	)

	// 根据TaskUuid查询下载任务
	if err = downloadTask.GetByTaskUuid(req.TaskUuid); err != nil {
		logger.Error(fmt.Sprintf("查询下载任务失败,taskUuid: %s, err: %v", req.TaskUuid, err))
		return resp, errors.New("下载任务不存在")
	}

	if downloadTask.ID == 0 {
		return resp, errors.New("下载任务不存在")
	}

	// 如果任务已完成，直接返回100%
	if downloadTask.Status == enums.ModelDownloadStatusCompleted {
		resp.CompressPercentage = 100
		return resp, nil
	}

	// 如果任务失败或取消，返回0%
	if downloadTask.Status == enums.ModelDownloadStatusFailed || downloadTask.Status == enums.ModelDownloadStatusCanceled {
		resp.CompressPercentage = 0
		return resp, nil
	}

	// 计算下载进度
	currentSize, err := f.calculateDownloadedSize(downloadTask.DownloadPath)
	if err != nil {
		logger.Error(fmt.Sprintf("计算已下载文件大小失败,path: %s, err: %v", downloadTask.DownloadPath, err))
		return resp, errors.New("获取下载进度失败")
	}

	// 计算百分比
	totalSize := float64(downloadTask.DownloadFileSize * 1024) // 数据库中存储的是KB，转换为字节
	if totalSize <= 0 {
		resp.CompressPercentage = 0
		return resp, nil
	}

	percentage := (float64(currentSize) / totalSize) * 100
	if percentage > 100 {
		percentage = 100
	}
	if percentage < 0 {
		percentage = 0
	}

	resp.CompressPercentage = uint(percentage)
	return resp, nil
}

func (f file_) FlashUploadCheck(req request.FlashUploadCheckReq) (response.FlashUploadCheckResp, error) {
	var (
		err      error
		fileInfo model.ShareModel
		resp     response.FlashUploadCheckResp
	)

	resp.Exist = enums.FlashUploadFileNotExist

	err = fileInfo.GetByHash(nil, req.FileHashHeader, req.FileHashMiddle, req.FileHashTail)
	if err != nil {
		logger.Error(fmt.Sprintf("根据hash查询文件失败,err: %v", err))
		return resp, err
	}
	if fileInfo.ID != 0 {
		resp.Exist = enums.FlashUploadFileExist
	}
	return resp, nil
}

func (f file_) FlashUpload(req request.FlashUploadReq, userId uint) error {
	var (
		err      error
		fileInfo model.ShareModel
	)

	err = fileInfo.GetByHash(nil, req.FileHashHeader, req.FileHashMiddle, req.FileHashTail)
	if err != nil {
		logger.Error(fmt.Sprintf("根据hash查询文件失败,err: %v", err))
		return err
	}
	if fileInfo.ID == 0 {
		return errors.New("文件不存在")
	}

	if err = utils.IsValidPathOrName([]string{req.Path, req.Filename}); err != nil {
		logger.Error("文件名或路径不合法,err:", err)
		return errors.New("文件名或路径不合法")
	}

	var user model.User
	if err = user.GetById(userId); err != nil {
		logger.Error(fmt.Sprintf("根据id查询用户失败,err: %v", err))
		return err
	}

	var (
		basePath string // 用户或pod的实际目录
	)

	if req.Catalogue != "" {
		if _, basePath, err = UserCatalogue.GetPathOfHost(req.Catalogue, userId, req.PodUuid); err != nil {
			logger.Error("获取路径失败", err)
			return errors.New("获取路径失败")
		} else {
			pre := fmt.Sprintf("basePath:%s ", basePath)
			logger.Info(pre)
			if req.PodUuid != "" {
				var pod model.Pod
				if err = pod.GetByUuid(req.PodUuid); err != nil {
					logger.Error(pre, "查询Pod信息失败", err, "podUuid:", req.PodUuid)
					return errors.New("查询Pod信息失败")
				}
				if power, err := UserCatalogue.GetNeedPrivilege(req.Catalogue); err != nil {
					logger.Error(pre, "获取权限信息失败,", err)
					return errors.New("获取权限信息失败")
				} else {
					if power == enums.CataLoguePrivilegeEnum.KOL {
						if pod.UserId != userId {
							logger.Error(pre, "无权限")
							return errors.New("无权限")
						}
					} else if power == enums.CataLoguePrivilegeEnum.SYS {
						logger.Error(pre, "无权限")
						return errors.New("无权限")
					} else if power != enums.CataLoguePrivilegeEnum.USER {
						logger.Error(pre, "无权限")
						return errors.New("无权限")
					}
				}
			}
		}
	} else {
		if user.ShortId == "" {
			if err = user.SetShortId(); err != nil {
				logger.Error(fmt.Sprintf("设置用户存储路径失败,err: %v", err))
				return err
			}
		}
		if user.ShortId == "" {
			logger.Error(fmt.Sprintf("设置用户存储路径失败, req: %+v", req))
			return errors.New("设置用户存储路径失败")
		}
		if len(user.PrivateStorage) < 10 {
			logger.Error("用户存储路径不正确，请重试，userPrivateStoragePath: %s", user.PrivateStorage)
			return errors.New("用户存储路径不正确，请重试")
		}
		if strings.HasPrefix(user.PrivateStorage, "/") == true || strings.HasSuffix(user.PrivateStorage, "/") == false {
			logger.Error("用户存储路径不正确，请重试，userPrivateStoragePath: %s", user.PrivateStorage)
			return errors.New("用户存储路径不正确，请重试")
		}
		basePath = filepath.Join(config.PrivateStorage, user.PrivateStorage)
		if _, err = os.Stat(basePath); os.IsNotExist(err) {
			logger.Info("用户存储路径不存在 开始创建存储路径", basePath, userId)
			if err := os.MkdirAll(basePath, os.ModePerm); err != nil {
				logger.Error(fmt.Sprintf("创建用户文件夹出错,err: %v", err))
				return err
			}
		} else if err != nil {
			logger.Error(fmt.Sprintf("检测用户存储路径失败,err: %v", err))
			return err
		}
		if _, err = os.Stat(config.PrivateStorage); err == nil || os.IsExist(err) {
			if !strings.HasPrefix(config.PrivateStorage, "/") {
				logger.Error("存储路径格式错误")
				return errors.New("存储路径格式错误")
			}
		} else {
			logger.Error(fmt.Sprintf("检测存储路径失败,err: %v", err))
			return err
		}
	}

	if !UserCatalogue.CheckBasePath(basePath) {
		logger.Error("根目录不正确", "userId:", userId, "  userBasePath:", basePath)
		return errors.New("根目录不正确")
	}

	filePathAbs := filepath.Join(basePath, req.Path, req.Filename)

	if strings.HasPrefix(basePath, "/mnt/user-data") {
		// 上传至用户目录则拷贝文件
		err = utils.CopyFile(fileInfo.OriginalPath, filePathAbs)
		if err != nil {
			logger.Error(fmt.Sprintf("复制文件失败,err: %v", err))
			return err
		}
	} else {
		// 上传至非用户目录则软链接
		err = utils.CreateSymbolLink(fileInfo.OriginalPath, filePathAbs)
		if err != nil {
			logger.Error(fmt.Sprintf("创建软链接失败,err: %v", err))
			return err
		}
	}

	return nil
}

func (f file_) CompressFile(req request.CompressFileReq, userId uint) (resp response.CompressFileResp, err error) {
	var (
		userInfo      model.User
		size          int64
		compressFiles []string
	)

	if len(req.FilePaths) == 0 {
		return resp, errors.New("请选择要压缩的文件")
	}

	// 判断路径是否合规
	if err = utils.IsValidPathOrName(req.FilePaths); err != nil {
		return resp, errors.New("非法路径")
	}

	// 获取用户信息
	if err = userInfo.GetById(userId); err != nil {
		logger.Error("获取用户信息失败", err)
		return resp, errors.New("获取用户信息失败")
	}

	if userInfo.ID == 0 {
		logger.Error(fmt.Sprintf("未查询到id为 %d 的用户信息", userId))
		return resp, errors.New("用户不存在")
	}

	if OutOfAmount(userId) {
		logger.Error("账户已欠费，请先充值再使用该功能", " userId:", userId, "  amount:", userInfo.Amount)
		return resp, errors.New("账户已欠费，请先充值再使用该功能")
	}

	userBasePath, err := f.PreCheckUserStorage(userInfo)
	if err != nil {
		return resp, err
	}

	// 将要压缩的文件去重
	req.FilePaths = utils.DeduplicateStrings(req.FilePaths)

	for _, fileItem := range req.FilePaths {
		compressFiles = append(compressFiles, filepath.Join(userBasePath, fileItem))
	}

	// 获取文件大小
	if size, err = FileService.getFileSizeBatch(compressFiles); err != nil {
		logger.Error("获取文件大小失败", err)
		return resp, errors.New("文件压缩失败")
	}

	if size*enums.FileSizeKB > 10*enums.FileSizeGB {
		return resp, errors.New("文件过大，总大小不能超过10G")
	}

	compressFileName := fmt.Sprintf("compress_%d.zip", time.Now().UnixMicro())
	datePath := time.Now().Format("20060102")
	compressPath := filepath.Join(config.PrivateStorage, enums.CompressedFilePath, datePath, strconv.Itoa(int(userInfo.ID)), compressFileName)
	// 调用接口压缩文件
	fileUuid, err := FileService.compressFiles(compressFiles, compressPath)
	if err != nil {
		logger.Error("调用接口压缩文件失败", err)
		return resp, errors.New("文件压缩失败")
	}

	// 存储压缩文件路径到redis中
	key := fmt.Sprintf("%s%s", enums.RedisKeyEnum.FileCompressedPath, fileUuid)
	err = redis_client.RedisSet(key, compressPath, enums.CompressedFileExpire)
	if err != nil {
		logger.Error("存储压缩文件路径到redis中失败", err)
		return resp, errors.New("文件压缩失败")
	}

	resp = response.CompressFileResp{
		FileUuid: fileUuid,
	}
	return
}

func (f file_) GetFileCompressProgress(req request.GetFileCompressProgressReq) (result uint, err error) {
	if result, err = FileService.getFileCompressProgress(req.FileUuid); err != nil {
		logger.Error("调用获取压缩进度接口失败,err:", err)
		return 0, errors.New("获取压缩进度失败")
	}
	return
}

func (f file_) DownloadCompressedFile(ctx *gin.Context, req request.FileCompressDownloadReq, userId uint) (err error) {
	var (
		userInfo model.User
	)
	// 获取用户信息
	if err = userInfo.GetById(userId); err != nil {
		logger.Error("获取用户信息失败，", err)
		return err
	}

	if OutOfAmount(userId) {
		logger.Error("账户已欠费，请先充值再使用该功能", " userId:", userInfo.ID, "  amount:", userInfo.Amount)
		return errors.New("账户已欠费，请先充值再使用该功能")
	}

	// 从缓存中获取文件地址
	key := fmt.Sprintf("%s%s", enums.RedisKeyEnum.FileCompressedPath, req.FileUuid)
	filePath, err := redis_client.RedisGet(key)
	if err != nil {
		logger.Error("获取文件地址失败，", err)
		if errors.Is(redis.Nil, err) {
			return errors.New("文件不存在")
		}
		return errors.New("下载文件失败")
	}

	// 首先创建一个随机的文件，然后再删除掉，防止nfs缓存
	fileDir := filepath.Dir(filePath)
	randomFileName := fmt.Sprintf(".tmp_%d_%s", time.Now().UnixNano(), uuid.New().String()[:8])
	randomFilePath := filepath.Join(fileDir, randomFileName)

	// 创建临时文件
	tmpFile, err := os.Create(randomFilePath)
	if err != nil {
		logger.Error("创建临时文件失败，跳过NFS缓存刷新: ", err)
	} else {
		tmpFile.Close()
		// 立即删除临时文件
		if removeErr := os.Remove(randomFilePath); removeErr != nil {
			logger.Error("删除临时文件失败: ", removeErr)
		}
	}

	info, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			logger.Error("路径不存在")
			return errors.New("文件不存在")
		} else {
			logger.Error("获取路径信息失败,err: ", err)
			return errors.New("下载文件失败")
		}
	}

	// 查询文件是否压缩完成
	compressPerc, err := f.getFileCompressProgress(req.FileUuid)
	if err != nil {
		logger.Error("获取文件压缩进度失败，", err)
		return errors.New("下载文件失败")
	}
	if compressPerc < 100 {
		return errors.New("文件还未压缩完成，请等待压缩完成后再下载")
	}

	// 检查文件类型
	if info.Mode().IsRegular() && !info.IsDir() {
		baseName := path.Base(filePath)
		// 将文件内容返回给用户进行下载
		//encodedFileName := url.QueryEscape(baseName)

		// 设置响应头，通知浏览器这是文件下载
		ctx.Header("Content-Disposition", "attachment; filename="+baseName)
		ctx.Header("Content-Type", "application/octet-stream")
		ctx.Header("Content-Length", strconv.FormatInt(info.Size(), 10))

		file, err1 := os.Open(filePath)
		if err1 != nil {
			logger.Error("打开文件失败", " err:", err1)
			return errors.New("下载文件失败")
		}
		defer file.Close()
		// 返回文件流
		http.ServeContent(ctx.Writer, ctx.Request, info.Name(), info.ModTime(), file)
		return
	}
	return errors.New("下载文件失败")
}

func (f file_) UserPrivateStatMap() (userPrivateSizeResp UserPrivateSizeResp) {
	err := PostFileStorage("/file/stat", nil, &userPrivateSizeResp, 30)
	if err != nil || userPrivateSizeResp.Code != 0 {
		logger.Error("获取用户的私有空间大小调用失败，将使用默认大小进行,接口 file/stat")
		emailReq := EmailReq{
			From:    "",
			To:      "<EMAIL>,<EMAIL>",
			Subject: "实例关闭失败，需要人工介入 " + time.Now().Format(jsontime.TimeFormat),
			Content: "获取用户的私有空间大小调用失败，将使用默认大小进行,接口 file/stat",
		}
		EmailService.SendWarn(emailReq)
	}
	return userPrivateSizeResp
}

func (f file_) GetDirSizeRemote(userId uint, dirPath string) (int64, error) {
	reqBody := DirSizeReq{
		BasePath: dirPath,
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		logger.Error("JSON序列化失败:", err)
		return 0, err
	}
	var result SizeResp
	err = PostFileStorage("/file/dir_size", jsonData, &result, 30)
	if err != nil {
		return 0, err
	}

	if result.Code != 0 {
		return 0, errors.New(result.Msg)
	}

	logger.Info("文件大小统计完成", dirPath)
	return result.Result.SumSize, nil
}

func (f file_) getFileSizeBatch(filePaths []string) (int64, error) {
	reqBody := FileSizeReq{
		FilePaths: filePaths,
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		logger.Error("JSON序列化失败:", err)
		return 0, err
	}
	var result SizeResp
	err = PostFileStorage(enums.FileOptUriSizeBatch, jsonData, &result, 30)
	if err != nil {
		return 0, err
	}

	if result.Code != 0 {
		return 0, errors.New(result.Msg)
	}

	logger.Info("文件大小统计完成", filePaths)
	return result.Result.SumSize, nil
}

func (f file_) compressFiles(files []string, compressPath string) (string, error) {
	reqBody := CompressFileReq{
		FilePaths:    files,
		CompressPath: compressPath,
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		logger.Error("JSON序列化失败:", err)
		return "", err
	}
	result := CompressFileResp{}
	err = PostFileStorage(enums.FileOptUriCompress, jsonData, &result, 60*10)
	if err != nil {
		return "", err
	}
	if result.Code != 0 {
		return "", errors.New(result.Msg)
	}
	logger.Info(fmt.Sprintf("文件压缩返回：%+v", result))
	return result.Result.FileUuid, nil
}

func (f file_) getFileCompressProgress(fileUuid string) (uint, error) {
	reqBody := GetFileCompressProgressReq{
		FileUuid: fileUuid,
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		logger.Error("JSON序列化失败:", err)
		return 0, err
	}
	result := GetFileCompressProgressResp{}
	err = PostFileStorage(enums.FileOptUriCompressProgress, jsonData, &result, 60*10)
	if err != nil {
		return 0, err
	}
	if result.Code != 0 {
		return 0, errors.New(result.Msg)
	}
	return result.Result.CompressPercentage, nil
}

func (f file_) PreCheckUserStorage(user model.User) (userBasePath string, err error) {
	if user.ShortId == "" {
		if err := user.SetShortId(); err != nil {
			logger.Error("设置用户存储路径失败", err)
			return "", errors.New("设置用户存储路径失败")
		}
	}
	if user.ShortId == "" {
		logger.Error("设置用户存储路径失败，请重试")
		return "", errors.New("设置用户存储路径失败，请重试")
	}
	if len(user.PrivateStorage) < 10 {
		logger.Error("用户存储路径不正确，请重试")
		return "", errors.New("用户存储路径不正确，请重试")
	}
	if strings.HasPrefix(user.PrivateStorage, "/") == true || strings.HasSuffix(user.PrivateStorage, "/") == false {
		logger.Error("用户存储路径格式不正确，请重试", user.PrivateStorage, " ", user.ID)
		return "", errors.New("用户存储路径格式不正确，请重试")
	}

	userBasePath = filepath.Join(config.PrivateStorage, user.PrivateStorage)
	if _, err = os.Stat(userBasePath); os.IsNotExist(err) {
		logger.Info("用户存储路径不存在 开始创建存储路径", userBasePath, user.ID)
		if err = os.MkdirAll(userBasePath, os.ModePerm); err != nil {
			logger.Error("创建用户文件夹出错", err)
			return "", errors.New("创建用户文件夹出错")
		}
	} else if err != nil {
		logger.Error("检测用户存储路径失败", err, user.ID)
		return
	}
	if config.PrivateStorage == "" {
		logger.Error("用户私有存储路径为空")
		return "", errors.New("用户私有存储路径为空")
	}
	if _, err = os.Stat(config.PrivateStorage); err == nil || os.IsExist(err) {
		logger.Info("config.PrivateStorage:", config.PrivateStorage)
		if strings.HasPrefix(config.PrivateStorage, "/") && strings.HasSuffix(config.PrivateStorage, "/") {

		} else {
			logger.Error("存储路径格式错误")
			return "", errors.New("存储路径格式错误")
		}
	} else {
		logger.Error("检测存储路径失败", err)
		return
	}

	if err = CheckPrivateStorage(); err != nil {
		logger.Error("基础存储验证失败", err, user.ID)
		return "", errors.New("基础存储验证失败")
	}
	return
}

// 下载文件 - 新增模型来源参数以便使用对应的认证信息
func (f file_) downloadFile(ctx context.Context, downloadUrl, fileSavePath string, modelSource enums.ModelSource) error {

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", downloadUrl, nil)
	if err != nil {
		return fmt.Errorf("创建下载请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", userAgent)

	// 根据模型来源添加认证头
	if modelSource == enums.ModelSourceHuggingFace {
		req.Header.Set("Authorization", "Bearer "+config.TokenHuggingface)
	} else if modelSource == enums.ModelSourceCivitai {
		req.Header.Set("Authorization", "Bearer "+config.TokenCivitai)
	}

	// 创建HTTP客户端，设置较长的超时时间
	client := &http.Client{
		Timeout: 60 * time.Minute,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("下载请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("下载API返回错误: %s - %s", resp.Status, string(body))
	}

	// 确保输出目录存在
	if err = utils.EnsureFilePathExists(fileSavePath); err != nil {
		return fmt.Errorf("创建文件路径失败: %v", err)
	}

	file, err := os.Create(fileSavePath)
	if err != nil {
		return fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer file.Close()

	// 写入下载的数据到文件
	bytesWritten, err := io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	logger.Info(fmt.Sprintf("成功下载文件: %s (大小: %s)", fileSavePath, utils.FormatFileSize(bytesWritten)))
	return nil
}

func (f file_) getHuggingFaceModelFile(url string) ([]ModelFileListResponse, string, error) {
	var (
		err       error
		modelName string
		resp      []ModelFileListResponse
	)
	resp = make([]ModelFileListResponse, 0)
	// 解析URL
	repoName, branch, err := f.parseHuggingFaceURL(url)
	if err != nil {
		logger.Error("解析模型地址失败,err: ", err)
		return resp, "", err
	}

	modelName = repoName
	parts := strings.Split(repoName, "/")
	if len(parts) >= 2 {
		modelName = parts[1]
	}
	logger.Info(fmt.Sprintf("正在查询仓库: %s (分支: %s)...", repoName, branch))

	// 获取仓库根目录文件列表
	var allFiles []HuggingFacFileInfo
	err = f.getAllFilesHuggingFace(repoName, branch, "", &allFiles)
	if err != nil {
		logger.Error("获取文件列表失败,err: ", err)
		return resp, "", err
	}

	for _, file := range allFiles {
		resp = append(resp, ModelFileListResponse{
			FileName:        file.Path,
			Sha256:          file.Lfs.Sha256,
			FileDownloadUrl: f.getDownloadURL(repoName, branch, file.Path),
			FileSize:        file.Size / enums.FileSizeKB,
		})
	}

	return resp, modelName, nil
}

func (f file_) getCivitaiModelFile(url string) ([]ModelFileListResponse, string, error) {
	var (
		err       error
		resp      []ModelFileListResponse
		modelName string
	)
	resp = make([]ModelFileListResponse, 0)
	// 确保URL不包含页面锚点和其他参数
	if idx := strings.Index(url, "#"); idx != -1 {
		url = url[:idx]
	}

	modelID, versionID, err := f.extractIDsFromURL(url)
	if err != nil {
		logger.Error(fmt.Sprintf("解析模型地址失败,err: %v", err))
		return resp, "", err
	}

	logger.Info("提取的模型ID: ", modelID)
	if versionID > 0 {
		logger.Info(fmt.Sprintf("提取的版本ID: %d", versionID))
	} else {
		logger.Info("未提供版本ID，将获取最新版本")
	}

	// 如果没有提供版本ID，获取模型信息并使用最新版本
	modelFiles := make([]CivitaiFile, 0)
	if versionID == 0 {
		modelInfo, err := f.getCivitaiModelInfo(modelID)
		if err != nil {
			logger.Error("获取模型信息失败,err: ", err)
			return resp, "", err
		}

		if len(modelInfo.ModelVersions) == 0 {
			logger.Error("模型没有版本，请检查模型ID")
			return resp, "", fmt.Errorf("模型没有版本，请检查模型ID")
		}

		// 使用最新版本（API返回的第一个版本）
		modelFiles = modelInfo.ModelVersions[0].Files
		modelName = modelInfo.Name // 获取模型名称
	} else {
		// 获取版本详细信息
		modelVersionInfo, err := f.getCivitaiModelVersionInfo(versionID)
		if err != nil {
			logger.Error("获取模型版本信息失败,err: ", err)
			return resp, "", err
		}
		modelFiles = modelVersionInfo.Files
		modelName = modelVersionInfo.Name
	}

	for _, file := range modelFiles {
		resp = append(resp, ModelFileListResponse{
			FileName:        file.Name,
			Sha256:          file.Hashes.SHA256,
			FileDownloadUrl: file.DownloadURL,
			FileSize:        int64(file.SizeKB),
		})
	}
	return resp, modelName, nil
}

// 从URL中提取模型ID和版本ID
func (f file_) extractIDsFromURL(url string) (int, int, error) {
	// 提取模型ID
	modelIDRegex := regexp.MustCompile(`/models/(\d+)`)
	modelIDMatches := modelIDRegex.FindStringSubmatch(url)
	if len(modelIDMatches) < 2 {
		return 0, 0, fmt.Errorf("无法从URL中提取模型ID")
	}
	modelID, err := strconv.Atoi(modelIDMatches[1])
	if err != nil {
		return 0, 0, fmt.Errorf("模型ID转换错误: %v", err)
	}

	// 提取版本ID（如果存在）
	versionID := 0
	versionIDRegex := regexp.MustCompile(`modelVersionId=(\d+)`)
	versionIDMatches := versionIDRegex.FindStringSubmatch(url)
	if len(versionIDMatches) >= 2 {
		versionID, err = strconv.Atoi(versionIDMatches[1])
		if err != nil {
			return modelID, 0, fmt.Errorf("版本ID转换错误: %v", err)
		}
	}

	return modelID, versionID, nil
}

// 获取模型信息 - 添加认证信息
func (f file_) getCivitaiModelInfo(modelID int) (*CivitaiModelResponse, error) {
	url := fmt.Sprintf("%s/models/%d", civitaiAPIBaseURL, modelID)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", userAgent)

	// 添加认证头
	if config.TokenCivitai != "" {
		req.Header.Set("Authorization", "Bearer "+config.TokenCivitai)
	}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("获取模型信息失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API返回错误: %s - %s", resp.Status, string(body))
	}

	var modelInfo CivitaiModelResponse
	if err := json.NewDecoder(resp.Body).Decode(&modelInfo); err != nil {
		return nil, fmt.Errorf("解析模型信息失败: %v", err)
	}

	return &modelInfo, nil
}

// 获取模型版本信息 - 添加认证信息
func (f file_) getCivitaiModelVersionInfo(versionID int) (*CivitaiModelVersionResponse, error) {
	url := fmt.Sprintf("%s/model-versions/%d", civitaiAPIBaseURL, versionID)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", userAgent)

	// 添加认证头
	if config.TokenCivitai != "" {
		req.Header.Set("Authorization", "Bearer "+config.TokenCivitai)
	}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("获取版本信息失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API返回错误: %s - %s", resp.Status, string(body))
	}

	var version CivitaiModelVersionResponse
	if err := json.NewDecoder(resp.Body).Decode(&version); err != nil {
		return nil, fmt.Errorf("解析版本信息失败: %v", err)
	}

	return &version, nil
}

// 从URL中提取模型仓库名和分支
func (f file_) parseHuggingFaceURL(url string) (string, string, error) {
	// 移除末尾的斜杠（如果有）
	url = strings.TrimSuffix(url, "/")

	// 基础正则表达式匹配 HuggingFace 仓库路径
	repoPattern := regexp.MustCompile(`huggingface\.co/([^/]+/[^/]+)(?:/tree/([^/]+))?`)
	matches := repoPattern.FindStringSubmatch(url)

	if len(matches) < 2 {
		return "", "", fmt.Errorf("无效的 HuggingFace URL 格式")
	}

	repoName := matches[1]
	branch := "main" // 默认分支

	// 如果URL中指定了分支
	if len(matches) > 2 && matches[2] != "" {
		branch = matches[2]
	}

	return repoName, branch, nil
}

// 递归获取目录中的所有文件 - 添加认证信息
func (f file_) getAllFilesHuggingFace(repoName, branch, path string, allFiles *[]HuggingFacFileInfo) error {
	// 构建API请求URL
	url := fmt.Sprintf("%s/%s/tree/%s/%s", huggingfaceAPIURL, repoName, branch, path)

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", userAgent)
	req.Header.Set("Accept", "application/json")

	// 添加认证头
	if config.TokenHuggingface != "" {
		req.Header.Set("Authorization", "Bearer "+config.TokenHuggingface)
	}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("API请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		// 如果是404，可能是遇到了LFS文件指针而非目录
		if resp.StatusCode == http.StatusNotFound {
			return nil
		}
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API返回错误: %s - %s", resp.Status, string(body))
	}

	// 解析响应
	var files []HuggingFacFileInfo
	body, _ := io.ReadAll(resp.Body)
	if err = json.Unmarshal(body, &files); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	// 处理每个文件/目录
	for _, file := range files {
		// 检查是否为目录
		isDir := strings.HasSuffix(file.Path, "/") || !strings.Contains(file.Path, ".")

		if isDir {
			// 递归处理子目录
			err := f.getAllFilesHuggingFace(repoName, branch, file.Path, allFiles)
			if err != nil {
				fmt.Printf("警告: 处理目录 %s 时出错: %v\n", file.Path, err)
			}
		} else {
			// 添加文件到结果列表
			*allFiles = append(*allFiles, file)
		}
	}

	return nil
}

// 构建文件下载URL
func (f file_) getDownloadURL(repoName, branch, filePath string) string {
	// 将所有路径组件正确编码
	pathComponents := strings.Split(filePath, "/")
	encodedPath := ""
	for i, component := range pathComponents {
		if i > 0 {
			encodedPath += "/"
		}
		// URL 编码路径组件（不使用标准URL编码是因为HF API有特殊要求）
		encodedPath += strings.ReplaceAll(component, " ", "%20")
	}

	// 构建下载URL
	return fmt.Sprintf("%s/%s/resolve/%s/%s", downloadBaseURL, repoName, branch, encodedPath)
}
